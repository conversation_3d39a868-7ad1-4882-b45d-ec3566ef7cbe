import { z } from 'zod';
import { BoundingRect } from './agent-state';

export type Action = {
  type: 'click' | 'fill' | 'acknowledge' | 'select';
  name: string;
  value?: string;
  coordinates: {
    x: number;
    y: number;
  };
  order: number; // Position in the visual flow, top to bottom
  isSubmitAction?: boolean; // Flag to identify submit/confirmation actions that should be last
};

// Action without coordinates for Phase 1 fast response
export type ActionWithoutCoordinates = {
  type: 'click' | 'fill' | 'acknowledge' | 'select';
  name: string;
  value?: string;
  order: number; // Position in the visual flow, top to bottom
  isSubmitAction?: boolean; // Flag to identify submit/confirmation actions that should be last
};

// Action with optional coordinates for intermediate processing
export type ActionWithOptionalCoordinates = {
  type: 'click' | 'fill' | 'acknowledge' | 'select';
  name: string;
  value?: string;
  coordinates?: {
    x: number;
    y: number;
  };
  order: number; // Position in the visual flow, top to bottom
  isSubmitAction?: boolean; // Flag to identify submit/confirmation actions that should be last
};

// Zod schemas for validation
const controlReasoningSchema = z.object({
  id: z.string(),
  status: z.enum(['included', 'excluded']),
  reasoning: z.string(),
  isControlEnabledReasoning: z.string(),
});

const formMetadataSchema = z.object({
  errors: z.array(z.string()).nullable(),
  controlReasoning: z.array(controlReasoningSchema).nullable(),
});

const formFieldSchema = z.object({
  id: z.string(),
  order: z.number(),
  label: z.string(),
  isLikelyDropdownReason: z.string(),
  isLikelyDropdown: z.boolean(),
  fieldControlType: z.enum([
    'select',
    'dropdown',
    'text',
    'password',
    'number',
    'checkbox',
    'checkboxgroup',
    'radiogroup',
    'textarea',
    'other',
  ]),
  actiontype: z.enum(['fill', 'select']),
  name: z.string(),
  checked: z.boolean(),
  isDontAskAgainControl: z.boolean(),
  options: z
    .array(
      z.object({
        value: z.string(),
        label: z.string(),
      }),
    )
    .nullable()
    .optional(),
});

const formButtonSchema = z.object({
  id: z.string(),
  order: z.number(),
  label: z.string(),
  variant: z.enum(['primary', 'secondary', 'link']),
  type: z.enum(['submit', 'click', 'device-ack']),
  actiontype: z.literal('click'),
  isDontAskAgainControl: z.boolean(),
});

const formControlsSchema = z.object({
  fields: z.array(formFieldSchema),
  buttons: z.array(formButtonSchema),
});

const extractionResultSchema = z.object({
  screenInfo: formMetadataSchema,
  controls: formControlsSchema,
});

const alertSchema = z.object({
  alertType: z.enum(['error', 'warning', 'security', 'notice']),
  alertText: z.string(),
});

const classificationMetadataSchema = z.object({
  title: z.string(),
  classificationReasoning: z.string(),
  authStateReasoning: z.string(),
  screenClass: z.enum([
    'profile-management-screen',
    'multi-factor-code-verification-screen',
    'multi-factor-push-approval-screen',
    'multi-factor-multiple-options-screen',
    'passkey-screen',
    'captcha-screen',
    'loading-screen',
    'trust-device-screen',
    'other',
    'logged-in-screen',
  ]),
  targetPlatform: z.string().nullable(),
  instruction: z.string(),
  standardLoginInstruction: z.string().nullable(),
  authState: z.enum(['authenticated', 'not-authenticated']),
  screenCodeReasoning: z.string(),
  screenCode: z.number().nullable(),
  alertsCount: z.number().nullable(),
  alertsAnalysis: z.string(),
  alerts: z.array(alertSchema).nullable(),
  alertsSummary: z.string(),
});

const classificationResultSchema = z.object({
  screenInfo: classificationMetadataSchema,
});

const pageStateResultSchema = z.object({
  extractionResult: extractionResultSchema,
  classificationResult: classificationResultSchema,
});

export type ClassificationResult = z.infer<typeof classificationResultSchema>;
export type ExtractionResult = z.infer<typeof extractionResultSchema>;
export type PageStateResult = z.infer<typeof pageStateResultSchema>;

export { pageStateResultSchema };

export type PageStateResultWithOptionalCoordinates = PageStateResult & {
  coordinatesResolved?: boolean; // Flag to track if coordinates are available
  coordinateResolutionPromise?: Promise<void>; // Promise for coordinate resolution
};

export type CaptchaBoundingBox = Omit<BoundingRect, 'id'>;

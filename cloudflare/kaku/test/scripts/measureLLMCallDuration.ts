import { AnthropicLLMRepository } from '../../src/llm/AnthropicLLMRepository';
import { LLMService } from '../../src/llm/LLMService';
import { OpenAILLMRepository } from '../../src/llm/OpenAILLMRepository';
import {
  htmxPromptInstructions,
  monolithicFormPromptInstructions,
  pageDetailsPromptInstructions,
  pageStateResultComparisonPromptInstructions,
} from '../../src/workflow/utils/constants';
import { extractFormJSON } from '../../src/workflow/utils/helpers';
import { imageToBase64 } from '../common/ImageHelpers';
import { GeminiLLMRepository } from '../../src/llm/GeminiLLMRepository';
import { FormVisionResult } from '../../src/form-generation/types';
import {
  FORM_VISION_CLASSIFICATION_PROMPT_V34,
  FORM_VISION_PROMPT_V6,
} from '../../src/workflow/prompts';
import { PlatformTypes } from '../../src/ui/constants';
import { GroqLLMRepository } from '../../src/llm/GroqLLMRepository';
import { CLASSIFICATION_RESPONSE_SCHEMA } from '../../src/llm/schemas/response-schemas';
import { makeParallelLLMCalls } from '../../src/llm/llm-parallel-calls';

const openAIKey = ''; //TODO(Insert the openAI key here to run this script)
const anthropicApiKey = ''; //TODO(Insert the anthropic key here to run this script)
const geminiApiKey = '';
const grokApiKey = '';
const noOfRacingLLMCalls = 2;
const geminiBaseURL =
  'https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/google-ai-studio';
const openAIBaseURL =
  'https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/openai';
const anthropicBaseURL =
  'https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/anthropic';
const grokBaseURL =
  'https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/groq';
const linkId = 'test_measureLLMCallDuration';

export async function testLLMFormGeneration() {
  //Check to confirm that the user has added the openAI key
  if (!grokApiKey) {
    console.log('Insert the grok api key to continue..');
    return;
  }
  const screenshot = await imageToBase64(
    `${import.meta.dirname.replace('/scripts', '/files/screenshots/google_2-step_verification.webp')}`,
  );

  //const geminiRepository = new GeminiLLMRepository(geminiApiKey, geminiBaseURL);
  const grokRepository = new GroqLLMRepository(grokApiKey, grokBaseURL);
  const llmService = new LLMService({
    primaryRepo: grokRepository,
    secondaryRepo: grokRepository,
  });

  console.log('-Grok-');
  console.log('Loading...');

  const start = Date.now();
  const response = await makeParallelLLMCalls(
    llmService,
    {
      linkId: linkId,
      platform: 'facebook',
      screenshot,
      skipCache: true,
      viewportWidth: 800,
      viewportHeight: 600,
    },
    FORM_VISION_PROMPT_V6,
    FORM_VISION_CLASSIFICATION_PROMPT_V34,
  );
  const end = Date.now();

  console.log(`\n\nGrok Duration: ${end - start}ms`);
  console.log(`\nGrok Response ${JSON.stringify(response)}\n`);
}
export async function testOpenAIIncorrectGoogleAuthStatus() {
  //Check to confirm that the user has added the openAI key
  if (!openAIKey) {
    console.log('Insert the openAI api key to continue..');
    return;
  }

  const googleErrorScreenshot = await imageToBase64(
    `${__dirname.replace('/scripts', '/files/screenshots/google_error.png')}`,
  );

  const openAIRepository = new OpenAILLMRepository(openAIKey, openAIBaseURL);

  const numberOfTests = 15;

  const pageTypeResults: string[] = [];

  for (let i = 1; i <= numberOfTests; i++) {
    try {
      const openAIResponse = await openAIRepository.getLLMResponse({
        linkId: linkId,
        platform: 'google',
        prompt: monolithicFormPromptInstructions,
        screenshot: googleErrorScreenshot,
        skipCache: true,
        viewportWidth: 800,
        viewportHeight: 600,
      });

      const result = extractFormJSON(openAIResponse.output_text);
      console.log(
        `\nTest ${i} - OpenAI Response:`,
        result.classificationResult.screenInfo.screenClass,
      );
      pageTypeResults.push(result.classificationResult.screenInfo.screenClass);
    } catch (error) {
      console.error(`❌ Test ${i} failed with an error:`, error);
    }
  }

  console.log('\n\nAll Page Type Results:', pageTypeResults);

  const pageTypeCount: Record<string, number> = {};
  pageTypeResults.forEach((type) => {
    if (pageTypeCount[type]) {
      pageTypeCount[type]++;
    } else {
      pageTypeCount[type] = 1;
    }
  });

  console.log('\nPage Type Count:', pageTypeCount);
  console.log('\n✅ All test completed.');
}

export async function measureSingleCallVsHTMXAndActionsAsTwoParallelCalls() {
  if (!openAIKey) {
    console.log('Insert the openAI api key in measureLLMCallDuration.ts file to continue..');
    return;
  }

  const facebookLoginScreenshot = await imageToBase64(
    `${__dirname.replace('/scripts', '/files/screenshots/facebook_login.webp')}`,
  );

  const llmRepository = new OpenAILLMRepository(openAIKey, openAIBaseURL);

  const llmService = new LLMService({
    primaryRepo: llmRepository,
    secondaryRepo: llmRepository,
  });

  console.log(`Single API call loading...`);

  const singleAPICallResponse = await llmService.raceLLMGetCalls(
    {
      linkId: linkId,
      platform: 'facebook',
      prompt: monolithicFormPromptInstructions,
      screenshot: facebookLoginScreenshot,
      skipCache: true,
      viewportWidth: 800,
      viewportHeight: 600,
    },
    noOfRacingLLMCalls,
    true,
  );

  console.log(`Single call response: ${singleAPICallResponse.output_text}`);
  console.log(`\n\nSingle Call Duration: ${singleAPICallResponse.callDuration}ms\n\n`);

  console.log(`Parallel calls loading...`);

  const htmxCallPromise = llmService.raceLLMGetCalls(
    {
      linkId: linkId,
      platform: 'facebook',
      prompt: htmxPromptInstructions,
      screenshot: facebookLoginScreenshot,
      skipCache: true,
      viewportWidth: 800,
      viewportHeight: 600,
    },
    noOfRacingLLMCalls,
    true,
  );

  const formFieldsCallPromise = llmService.raceLLMGetCalls(
    {
      linkId: linkId,
      platform: 'facebook',
      prompt: pageDetailsPromptInstructions,
      screenshot: facebookLoginScreenshot,
      skipCache: true,
      viewportWidth: 800,
      viewportHeight: 600,
    },
    noOfRacingLLMCalls,
    true,
  );

  const htmxValue = await htmxCallPromise;
  const formFieldsValue = await formFieldsCallPromise;

  const highestParallelDuration: number =
    htmxValue.callDuration > formFieldsValue.callDuration
      ? htmxValue.callDuration
      : formFieldsValue.callDuration;

  console.log(`HTMX Response: ${htmxValue.output_text}\n\n`);
  console.log(`FormFields Response: ${formFieldsValue.output_text}`);

  console.log(`\nSingle Call Duration: ${singleAPICallResponse.callDuration}ms`);
  console.log(`\nHTMX Call Duration: ${htmxValue.callDuration}ms`);
  console.log(`FormFields Call Duration: ${formFieldsValue.callDuration}ms\n`);

  console.log(
    `Single call took ${singleAPICallResponse.callDuration}ms and Parallel calls took ${highestParallelDuration}ms`,
  );
  console.log(`Difference is ${singleAPICallResponse.callDuration - highestParallelDuration}ms`);
}

export async function detectStateChangeByComparingPageStateResultWithLatestScreenshot() {
  if (!geminiApiKey) {
    console.log('Insert the gemini api key to continue..');
    return;
  }

  const geminiRepository = new GeminiLLMRepository(geminiApiKey, geminiBaseURL);
  const llmService = new LLMService({
    primaryRepo: geminiRepository,
    secondaryRepo: geminiRepository,
  });

  console.log('---GEMINI--');

  const testScenarios: LLMChangeFormat[] = [
    {
      platform: 'facebook',
      initialImage: 'facebook_login.webp',
      comparisons: [
        { image: 'facebook_login.webp', expectedResponse: false },
        { image: 'facebook_radios.webp', expectedResponse: true },
        { image: 'facebook_wrong_credentials.webp', expectedResponse: true },
      ],
    },
    {
      platform: 'google',
      initialImage: 'google_login.webp',
      comparisons: [
        { image: 'google_login.webp', expectedResponse: false },
        { image: 'google_2step_verification.webp', expectedResponse: true },
        { image: 'facebook_wrong_credentials.webp', expectedResponse: true },
      ],
    },
    {
      platform: 'github',
      initialImage: 'github_login.webp',
      comparisons: [
        { image: 'github_login.webp', expectedResponse: false },
        { image: 'github_logged_in.webp', expectedResponse: true },
        { image: 'github_setup_passkey.webp', expectedResponse: true },
      ],
    },
  ];
  for (const platformTest of testScenarios) {
    console.log(`Tests for ${platformTest.platform}`);
    for (const finalImage of platformTest.comparisons) {
      console.log(
        `Comparison for INITIAL: ${platformTest.initialImage} and FINAL: ${finalImage.image}`,
      );
      await doTheComparison(
        llmService,
        platformTest.platform,
        `${__dirname.replace('/scripts', `/files/screenshots/${platformTest.initialImage}`)}`,
        `${__dirname.replace('/scripts', `/files/screenshots/${finalImage.image}`)}`,
        finalImage.expectedResponse,
      );
    }
  }
}
const action = process.argv[2];

switch (action) {
  case 'testllmFormGeneration':
    testLLMFormGeneration();
    break;
  case 'testOpenAIIncorrectGoogleAuthStatus':
    testOpenAIIncorrectGoogleAuthStatus();
    break;
  case 'measureParallelCallsPerformance':
    measureSingleCallVsHTMXAndActionsAsTwoParallelCalls();
    break;
  case 'detectChangeByComparingPageStateResult':
    detectStateChangeByComparingPageStateResultWithLatestScreenshot();
    break;
  default:
    console.error(`Unknown or missing action: "${action}"`);
    process.exit(1);
}

async function doTheComparison(
  llmService: LLMService,
  platformId: PlatformTypes,
  initialImage: string,
  nextStateImage: string,
  expectedResponse: boolean,
) {
  const firstImage = await imageToBase64(initialImage);
  const firstLLMResponse = await llmService.getLLMResponse({
    linkId: linkId,
    platform: platformId,
    prompt: FORM_VISION_PROMPT_V6,
    screenshot: firstImage,
    skipCache: true,
    viewportWidth: 800,
    viewportHeight: 600,
  });

  const parsedResponse = (() => {
    try {
      return JSON.parse(firstLLMResponse.output_text) as FormVisionResult;
    } catch (e) {
      console.error(
        `Couldn't parse the output response to FormVisionResult. Response: ${firstLLMResponse.output_text}`,
      );
      throw e;
    }
  })();
  //console.log(`First call completed. Result took ${firstLLMResponse.callDuration}ms\n`)

  //DETECT STATE CHANGE
  const secondImage = await imageToBase64(nextStateImage);
  const secondLLMResponse = await llmService.detectStateChangeFromUserAgentState({
    linkId: linkId,
    platform: platformId,
    prompt: pageStateResultComparisonPromptInstructions,
    agentVisionResultState: parsedResponse,
    screenshot: secondImage,
    skipCache: true,
    viewportWidth: 800,
    viewportHeight: 600,
  });

  console.log(`LLMResponse: ${secondLLMResponse}`);
  if (secondLLMResponse === expectedResponse) {
    console.log('CORRECT response!!!\n');
  } else {
    console.log(`WRONG response!!!, Expected ${expectedResponse} but got ${secondLLMResponse}\n`);
  }
}

type LLMChangeFormat = {
  platform: PlatformTypes;
  initialImage: string;
  comparisons: ComparisonType[];
};

interface ComparisonType {
  image: string;
  expectedResponse: boolean;
}

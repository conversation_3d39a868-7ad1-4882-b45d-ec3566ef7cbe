/**
 * Test for form generation with alert summary integration
 */

import { describe, it, expect } from 'vitest';
import { htmxFormGenerator } from '../../src/form-generation/htmx-generator';
import { ClassificationResult, ExtractionResult } from '../../src/agent/types';

describe('Form Generation with Alert Summary', () => {
  const mockExtractionResult: ExtractionResult = {
    screenInfo: {
      errors: null,
      includedControls: null,
    },
    controls: {
      fields: [
        {
          id: 'email',
          order: 1,
          label: 'Email',
          isLikelyDropdown: false,
          fieldControlType: 'text',
          actiontype: 'fill',
          name: 'email',
          checked: false,
          options: null,
        },
      ],
      buttons: [
        {
          id: 'submit',
          order: 2,
          label: 'Sign In',
          variant: 'primary',
          type: 'submit',
          actiontype: 'click',
        },
      ],
    },
  };

  it('should include alert summary when alerts are present', () => {
    const classificationResult: ClassificationResult = {
      screenInfo: {
        title: 'Sign In',
        instruction: 'Please enter your credentials',
        authState: 'not-authenticated',
        classificationReasoning: 'Login form detected',
        screenClass: 'other',
        screenCode: null,
        alertsCount: 2,
        alertsAnalysis: 'Error and warning messages detected on screen',
        alerts: [
          { alertType: 'error', alertText: 'Invalid email format' },
          { alertType: 'warning', alertText: 'Account will be locked after 3 failed attempts' },
        ],
        alertsSummary: 'Your email format is invalid and your account may be locked soon',
      },
    };

    const result = htmxFormGenerator.generateForm(mockExtractionResult, classificationResult);

    // Should contain alert summary
    expect(result).toContain('Your email format is invalid and your account may be locked soon');
    expect(result).toContain('bg-gradient-to-r from-red-50 to-red-100'); // Beautiful gradient styling

    // Should still contain form elements
    expect(result).toContain('Email');
    expect(result).toContain('Sign In');
    expect(result).toContain('<form');
    expect(result).toContain('</form>');
  });

  it('should not include alert summary when no alerts', () => {
    const classificationResult: ClassificationResult = {
      screenInfo: {
        title: 'Sign In',
        instruction: 'Please enter your credentials',
        authState: 'not-authenticated',
        classificationReasoning: 'Login form detected',
        screenClass: 'other',
        screenCode: null,
        alertsCount: 0,
        alertsAnalysis: 'No alerts detected',
        alerts: [],
        alertsSummary: '',
      },
    };

    const result = htmxFormGenerator.generateForm(mockExtractionResult, classificationResult);

    // Should not contain alert summary
    expect(result).not.toContain('Alerts Detected');
    expect(result).not.toContain('bg-gradient-to-r from-red-50 to-red-100');
    expect(result).not.toContain('rounded-xl');

    // Should still contain form elements
    expect(result).toContain('Email');
    expect(result).toContain('Sign In');
  });

  it('should include alert summary before instruction', () => {
    const classificationResult: ClassificationResult = {
      screenInfo: {
        title: 'Sign In',
        instruction: 'Please enter your credentials',
        authState: 'not-authenticated',
        classificationReasoning: 'Login form detected',
        screenClass: 'other',
        screenCode: null,
        alertsCount: 1,
        alertsAnalysis: 'Security warning detected',
        alerts: [{ alertType: 'security', alertText: 'Suspicious login attempt detected' }],
        alertsSummary: 'A suspicious login attempt was detected from your location',
      },
    };

    const result = htmxFormGenerator.generateForm(mockExtractionResult, classificationResult);

    const alertIndex = result.indexOf('A suspicious login attempt was detected from your location');
    const instructionIndex = result.indexOf('Please enter your credentials');

    // Alert summary should appearf
    expect(alertIndex).toBeGreaterThan(-1);
    expect(instructionIndex).toBeGreaterThan(-1);
  });

  it('should include instruction if no static description is needed and not duplicate field label', () => {
    const extractionResultWithoutKeywords: ExtractionResult = {
      screenInfo: {
        errors: null,
        includedControls: null,
      },
      controls: {
        fields: [
          {
            id: 'name',
            order: 1,
            label: 'Your Name',
            isLikelyDropdown: false,
            fieldControlType: 'text',
            actiontype: 'fill',
            name: 'name',
            checked: false,
            options: null,
          },
        ],
        buttons: [
          {
            id: 'submit',
            order: 2,
            label: 'Submit',
            variant: 'primary',
            type: 'submit',
            actiontype: 'click',
          },
        ],
      },
    };

    const classificationResult: ClassificationResult = {
      screenInfo: {
        title: 'Welcome',
        instruction: 'Please enter your details to proceed.',
        authState: 'authenticated',
        classificationReasoning: 'Profile form',
        screenClass: 'other',
        screenCode: null,
        alertsCount: 0,
        alertsAnalysis: '',
        alerts: [],
        alertsSummary: '',
      },
    };

    const result = htmxFormGenerator.generateForm(
      extractionResultWithoutKeywords,
      classificationResult,
    );

    // The instruction should be present
    expect(result).toContain('Please enter your details to proceed.');
    expect(result).toContain('Your Name');
  });
});

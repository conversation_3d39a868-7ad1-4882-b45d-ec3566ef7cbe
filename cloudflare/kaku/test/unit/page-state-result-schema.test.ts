/**
 * Test for PageStateResult Zod schema validation
 */

import { describe, expect, it } from 'vitest';
import { pageStateResultSchema } from '../../src/agent/types/llm-result';

describe('PageStateResult Schema Validation', () => {
  it('should validate a valid PageStateResult', () => {
    const validPageStateResult = {
      extractionResult: {
        screenInfo: {
          errors: null,
          includedControls: [
            {
              id: 'email',
              status: 'included' as const,
              isExcludedReason: 'None' as const,
            },
          ],
        },
        controls: {
          fields: [
            {
              id: 'email',
              order: 1,
              label: 'Email',
              isLikelyDropdown: false,
              fieldControlType: 'text' as const,
              actiontype: 'fill' as const,
              name: 'email',
              checked: false,
              isDontAskAgainControl: false,
              options: null,
            },
          ],
          buttons: [
            {
              id: 'submit',
              order: 2,
              label: 'Sign In',
              variant: 'primary' as const,
              type: 'submit' as const,
              actiontype: 'click' as const,
              isDontAskAgainControl: false,
            },
          ],
        },
      },
      classificationResult: {
        screenInfo: {
          title: 'Login Screen',
          instruction: 'Enter your credentials',
          standardLoginInstruction: 'Enter your credentials',
          authState: 'not-authenticated' as const,
          classificationReasoning: 'This is a login form',
          screenClass: 'other' as const,
          screenCode: null,
          alertsCount: 0,
          alertsAnalysis: 'No alerts detected',
          alerts: [],
          alertsSummary: 'No issues found',
        },
      },
    };

    expect(() => pageStateResultSchema.parse(validPageStateResult)).not.toThrow();
    const result = pageStateResultSchema.parse(validPageStateResult);
    expect(result).toEqual(validPageStateResult);
  });

  it('should reject invalid extractionResult', () => {
    const invalidPageStateResult = {
      extractionResult: {
        screenInfo: {
          errors: null,
          includedControls: null,
        },
        controls: {
          fields: [
            {
              id: 'email',
              order: 1,
              label: 'Email',
              isLikelyDropdown: false,
              fieldControlType: 'invalid-type', // Invalid field type
              actiontype: 'fill',
              name: 'email',
              checked: false,
            },
          ],
          buttons: [],
        },
      },
      classificationResult: {
        screenInfo: {
          title: 'Login Screen',
          instruction: 'Enter your credentials',
          authState: 'not-authenticated',
          classificationReasoning: 'This is a login form',
          screenClass: 'other',
          screenCode: null,
          alertsCount: 0,
          alertsAnalysis: 'No alerts detected',
          alerts: [],
          alertsSummary: 'No issues found',
        },
      },
    };

    expect(() => pageStateResultSchema.parse(invalidPageStateResult)).toThrow();
  });

  it('should reject invalid classificationResult', () => {
    const invalidPageStateResult = {
      extractionResult: {
        screenInfo: {
          errors: null,
          includedControls: null,
        },
        controls: {
          fields: [],
          buttons: [],
        },
      },
      classificationResult: {
        screenInfo: {
          title: 'Login Screen',
          instruction: 'Enter your credentials',
          authState: 'invalid-auth-state', // Invalid auth state
          classificationReasoning: 'This is a login form',
          screenClass: 'other',
          screenCode: null,
          alertsCount: 0,
          alertsAnalysis: 'No alerts detected',
          alerts: [],
          alertsSummary: 'No issues found',
        },
      },
    };

    expect(() => pageStateResultSchema.parse(invalidPageStateResult)).toThrow();
  });

  it('should reject missing required fields', () => {
    const incompletePageStateResult = {
      extractionResult: {
        screenInfo: {
          errors: null,
          includedControls: null,
        },
        controls: {
          fields: [],
          buttons: [],
        },
      },
      // Missing classificationResult
    };

    expect(() => pageStateResultSchema.parse(incompletePageStateResult)).toThrow();
  });

  it('should validate alerts array correctly', () => {
    const pageStateResultWithAlerts = {
      extractionResult: {
        screenInfo: {
          errors: ['Invalid credentials'],
          includedControls: null,
        },
        controls: {
          fields: [],
          buttons: [],
        },
      },
      classificationResult: {
        screenInfo: {
          title: 'Login Error',
          instruction: 'Please check your credentials',
          standardLoginInstruction: 'Please check your credentials',
          authState: 'not-authenticated' as const,
          classificationReasoning: 'Login failed',
          screenClass: 'other' as const,
          screenCode: null,
          alertsCount: 1,
          alertsAnalysis: 'Error detected',
          alerts: [
            {
              alertType: 'error' as const,
              alertText: 'Invalid username or password',
            },
          ],
          alertsSummary: 'Login failed due to invalid credentials',
        },
      },
    };

    expect(() => pageStateResultSchema.parse(pageStateResultWithAlerts)).not.toThrow();
    const result = pageStateResultSchema.parse(pageStateResultWithAlerts);
    expect(result.classificationResult.screenInfo.alerts).toHaveLength(1);
    expect(result.classificationResult.screenInfo.alerts?.[0].alertType).toBe('error');
  });
});
